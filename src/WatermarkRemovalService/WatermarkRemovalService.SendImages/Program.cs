using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Abstractions;
using Microsoft.Extensions.Logging.Console;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Extensions.Http;
using Refit;
using System.Net.Http.Headers;
using System.Text;
using WatermarkRemovalService.SendImages;


// Build configuration
var configuration = new ConfigurationBuilder()
    .SetBasePath(Directory.GetCurrentDirectory())
    .AddJsonFile("appsettings.json", optional: false)
    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT")
        ?? "Production"}.json", optional: true)
    .Build();

var connectionString = configuration.GetConnectionString("DataEntryContext")
    ?? throw new InvalidOperationException("Connection string 'DataEntryContext' not found.");
var bearerToken = configuration["ApiSettings:BearerToken"]
    ?? throw new InvalidOperationException("Bearer token not found in configuration.");
var apiBaseUrl = configuration["ApiSettings:BaseUrl"]
    ?? throw new InvalidOperationException("API base URL not found in configuration.");
var officeIds = configuration.GetSection("OfficeIds").Get<int[]>()
    ?? throw new InvalidOperationException("OfficeIds not found in configuration.");

// Setup service collection for dependency injection and HTTP client with retry policy
var services = new ServiceCollection();

// Configure logging
services.AddLogging(builder =>
{
    builder.AddConfiguration(configuration.GetSection("Logging"));
    builder.AddConsole(options =>
    {
        options.FormatterName = "custom";
    });
    builder.AddConsoleFormatter<CustomConsoleFormatter, CustomConsoleFormatterOptions>();
});

// Build service provider to get logger
var serviceProvider = services.BuildServiceProvider();
var logger = serviceProvider.GetRequiredService<ILogger<Program>>();

// Configure HttpClient with Polly retry policy
services.AddHttpClient("ImageProcessingApi", client =>
{
    client.BaseAddress = new Uri(apiBaseUrl);
    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", bearerToken);
    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
})
.AddPolicyHandler(GetRetryPolicy(logger));

// Rebuild service provider with all services
serviceProvider = services.BuildServiceProvider();

// Get the HTTP client factory and create the client
var httpClientFactory = serviceProvider.GetRequiredService<IHttpClientFactory>();
var httpClient = httpClientFactory.CreateClient("ImageProcessingApi");

// Create Refit client with the configured HttpClient
var apiClient = RestService.For<IImageProcessingApi>(httpClient, new RefitSettings
{
    ContentSerializer = new SystemTextJsonContentSerializer()
});

try
{
    logger.LogInformation("Starting image processing task...");

    // Process images
    await ProcessImagesAsync(connectionString, officeIds, apiClient, logger);

    logger.LogInformation("Image processing task completed successfully.");
}
catch (Exception ex)
{
    logger.LogError(ex, "Error: {Message}", ex.Message);
    logger.LogError("Stack trace: {StackTrace}", ex.StackTrace);
    Environment.Exit(1);
}

// Configure retry policy for HTTP requests
static IAsyncPolicy<HttpResponseMessage> GetRetryPolicy(ILogger logger)
{
    return HttpPolicyExtensions
        .HandleTransientHttpError() // Handles HttpRequestException and 5XX, 408 status codes
        .Or<TaskCanceledException>() // Handle timeout
        .WaitAndRetryAsync(
            retryCount: 3,
            sleepDurationProvider: retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)), // Exponential backoff: 2s, 4s, 8s
            onRetry: (outcome, timespan, retryCount, context) =>
            {
                logger.LogInformation("Retry attempt {RetryCount} after {Delay} seconds delay. Reason: {Reason}",
                    retryCount, timespan.TotalSeconds, outcome.Exception?.Message ?? outcome.Result?.StatusCode.ToString());
            });
}

static async Task ProcessImagesAsync(
    string connectionString,
    int[] officeIds,
    IImageProcessingApi apiClient,
    ILogger logger)
{
    using var connection = new SqlConnection(connectionString);
    await connection.OpenAsync();

    // Get all pending images
    var images = await connection.QueryAsync<ImageRecord>(@"
        select lp.ListingPictureID as ListingPictureId,
               lp.listingid as ListingId,
               l.listingnumber as ListingNumber,
               lp.source as ImageUrl
          from ListingPictures lp
         inner join Listings l on l.ListingID = lp.ListingID
          left join Sales s on l.ListingID = s.ListingID and s.SaleStatusID not in (3,6)
          left join Rentals r on l.ListingID = r.ListingID and r.RentalStatusID not in (3,8)
         where l.ListingOfficeID IN @officeIds
           and pictureTypeId  = 0
           and lp.source like '%jagmedia1%'
           and isnull(s.SaleStatusID, r.RentalStatusID) is not null
           and l.ListingID not in (select ListingId from ListingMediaProcessingLog)
    ", new { officeIds });

    if (images == null || !images.Any())
    {
        logger.LogInformation("No pending images found to process.");
        return;
    }

    const int batchSize = 1;

    const int failedThreshold = 5;

    var total = images.Count();
    var pageSize = total < batchSize ? total : batchSize;

    logger.LogInformation("Processing {PageSize} pending images out of total {Total}.", pageSize, total);

    int processed = 0;
    int successful = 0;
    int failed = 0;

    foreach (var image in images.Take(pageSize))
    {
        int recordId = 0;

        try
        {
            logger.LogInformation("Processing image '{ImageUrl}' (id: {ListingPictureId}) for listing #{ListingNumber}",
                image.ImageUrl, image.ListingPictureId, image.ListingNumber);

            // Insert initial record into processing log
            recordId = await connection.ExecuteScalarAsync<int>(@"
                insert into ListingMediaProcessingLog
                (ListingPictureId, ListingId, ListingNumber, SourceImageUrl, Status)
                values (@ListingPictureId, @ListingId, @ListingNumber, @ImageUrl, 'New');
                select cast(scope_identity() as int);
            ",
            new
            {
                image.ListingPictureId,
                image.ListingId,
                image.ListingNumber,
                image.ImageUrl
            });

            // Make API call
            var request = new ProcessImageRequest(image.ImageUrl);
            var response = await apiClient.ProcessImageAsync(request);

            if (response.Code == 0)
            {
                // update log status to Submitted with TaskId
                await connection.ExecuteAsync(@"
                    update ListingMediaProcessingLog
                       set Status = 'Submitted',
                           StatusCode = @Code,
                           StatusMessage = @Message,
                           TaskId = @TaskId,
                           UpdatedOn = GETDATE()
                     where Id = @Id
                ",
                new
                {
                    Id = recordId,
                    response.Code,
                    response.Message,
                    response.Data.TaskId
                });

                successful++;
                logger.LogInformation("✓ Successfully processed image id: {ListingId}", image.ListingId);
            }
            else
            {
                // Update status to Failed
                await connection.ExecuteAsync(@"
                    update ListingMediaProcessingLog
                       set Status = 'Failed',
                           StatusCode = @Code,
                           StatusMessage = @Message,
                           UpdatedOn = GETDATE()
                     where Id = @Id
                ",
                new
                {
                    Id = recordId,
                    response.Code,
                    response.Message
                });

                failed++;
                logger.LogError("✗ Failed to process image id: {ListingPictureId}: {Message}", image.ListingPictureId, response.Message);
            }
        }
        catch (Exception ex)
        {
            // Update status to Failed
            await connection.ExecuteAsync(@"
                    update ListingMediaProcessingLog
                       set Status = 'Failed',
                           StatusCode = -1,
                           StatusMessage = @Message,
                           UpdatedOn = GETDATE()
                     where Id = @Id
                ",
            new
            {
                Id = recordId,
                ex.Message
            });

            failed++;
            logger.LogError(ex, "✗ Error processing image id: {ListingPictureId}: {Message}", image.ListingPictureId, ex.Message);
        }

        processed++;

        if (failed >= failedThreshold)
        {
            logger.LogError("Stopped processing due to too many failed attempts.");
            break;
        }
        if (processed % 50 == 0)
        {
            logger.LogInformation("Progress: {Processed}/{PageSize} processed.", processed, pageSize);
        }
    }

    logger.LogInformation("Processing completed:");
    logger.LogInformation("Total processed: {Processed}", processed);
    logger.LogInformation("Successful: {Successful}", successful);
    logger.LogInformation("Failed: {Failed}", failed);
}

// Custom console formatter options
public sealed class CustomConsoleFormatterOptions : ConsoleFormatterOptions
{
}

// Custom console formatter
public sealed class CustomConsoleFormatter : ConsoleFormatter, IDisposable
{
    private readonly IDisposable? _optionsReloadToken;
    private CustomConsoleFormatterOptions _formatterOptions;

    public CustomConsoleFormatter(IOptionsMonitor<CustomConsoleFormatterOptions> options)
        : base("custom")
    {
        _optionsReloadToken = options.OnChange(ReloadLoggerOptions);
        _formatterOptions = options.CurrentValue;
    }

    private void ReloadLoggerOptions(CustomConsoleFormatterOptions options)
    {
        _formatterOptions = options;
    }

    public override void Write<TState>(in LogEntry<TState> logEntry, IExternalScopeProvider? scopeProvider, TextWriter textWriter)
    {
        string? message = logEntry.Formatter?.Invoke(logEntry.State, logEntry.Exception);
        if (message is null)
        {
            return;
        }

        // Format: [2025/06/07 00:00:00.008] | Info | AddressGeocodingService | Reading GeocodingQueue table
        var timestamp = DateTime.Now.ToString("yyyy/MM/dd HH:mm:ss.fff");
        var logLevel = GetLogLevelString(logEntry.LogLevel);
        var categoryName = logEntry.Category;

        textWriter.WriteLine($"[{timestamp}] | {logLevel} | {categoryName} | {message}");

        if (logEntry.Exception != null)
        {
            textWriter.WriteLine($"[{timestamp}] | {logLevel} | {categoryName} | Exception: {logEntry.Exception}");
        }
    }

    private static string GetLogLevelString(LogLevel logLevel)
    {
        return logLevel switch
        {
            LogLevel.Trace => "Trace",
            LogLevel.Debug => "Debug",
            LogLevel.Information => "Info",
            LogLevel.Warning => "Warn",
            LogLevel.Error => "Error",
            LogLevel.Critical => "Critical",
            _ => logLevel.ToString()
        };
    }

    public void Dispose()
    {
        _optionsReloadToken?.Dispose();
    }
}
